const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        subscriptionStatus: true,
        subscriptionExpiresAt: true,
      },
    });
    
    console.log('All users:');
    users.forEach(user => {
      console.log(`- ${user.email}:`);
      console.log(`  ID: ${user.id}`);
      console.log(`  Status: ${user.subscriptionStatus}`);
      console.log(`  Expires: ${user.subscriptionExpiresAt}`);
      console.log(`  Is Active: ${user.subscriptionStatus === 'ACTIVE' && user.subscriptionExpiresAt && user.subscriptionExpiresAt > new Date()}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Error checking users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
