const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Hash the password
    const hashedPassword = await bcrypt.hash('Password123!', 12);
    
    // Create a free user (no subscription)
    const freeUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        emailVerified: new Date(), // Mark as verified
        subscriptionStatus: 'FREE', // No subscription
        subscriptionExpiresAt: null,
      },
    });
    
    console.log('✅ Created free user:', freeUser.email);
    
    // Create a premium user (with subscription)
    const premiumUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        emailVerified: new Date(), // Mark as verified
        subscriptionStatus: 'ACTIVE', // Active subscription
        subscriptionTier: 'CAREER_TRANSITION', // Set tier
        subscriptionExpiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        subscriptionStartedAt: new Date(), // Set start date
      },
    });

    // Create subscription record in subscriptions table
    const subscription = await prisma.subscription.create({
      data: {
        userId: premiumUser.id,
        tier: 'CAREER_TRANSITION',
        status: 'ACTIVE',
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        autoRenew: false,
        stripeProductId: 'career-transition-package', // Mock product ID
      },
    });

    console.log('✅ Created premium user:', premiumUser.email);
    console.log('✅ Created subscription record:', subscription.id);
    
  } catch (error) {
    console.error('❌ Error creating test users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
