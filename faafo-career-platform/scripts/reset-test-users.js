const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function resetTestUsers() {
  try {
    console.log('🗑️ Deleting existing test users...');
    
    // Delete subscriptions first (foreign key constraint)
    await prisma.subscription.deleteMany({
      where: {
        user: {
          email: {
            in: ['<EMAIL>', '<EMAIL>']
          }
        }
      }
    });
    
    // Delete users
    await prisma.user.deleteMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>']
        }
      }
    });
    
    console.log('✅ Deleted existing test users');
    
  } catch (error) {
    console.error('❌ Error resetting test users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetTestUsers();
