'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import EnhancedProfileForm from '@/components/profile/EnhancedProfileForm';
import { ErrorBoundary } from '@/components/unified-error-boundary';
import PageLayout from '@/components/layout/PageLayout';
import { FormLoadingState, PageErrorState } from '@/components/ui/page-loading-states';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Mail, CheckCircle, AlertCircle } from 'lucide-react';
import PremiumGuard from '@/components/auth/PremiumGuard';

interface ProfileData {
  id?: string;
  bio?: string;
  profilePictureUrl?: string;
  socialMediaLinks?: { [key: string]: string };

  // Phase 2 Enhanced Profile Fields
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  company?: string;
  location?: string;
  phoneNumber?: string;
  website?: string;

  // Career-related fields
  careerInterests?: string[];
  skillsToLearn?: string[];
  experienceLevel?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  currentIndustry?: string;
  targetIndustry?: string;

  // Profile completion tracking
  profileCompletionScore?: number;
  lastProfileUpdate?: string;

  // Preferences
  weeklyLearningGoal?: number;
  emailNotifications?: boolean;
  profileVisibility?: 'PRIVATE' | 'PUBLIC' | 'COMMUNITY_ONLY';
  profilePublic?: boolean;
  showEmail?: boolean;
  showPhone?: boolean;
}

function ProfilePageContent() {
  const { data: session, status } = useSession();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [emailVerified, setEmailVerified] = useState<boolean | null>(null);
  const [isResendingVerification, setIsResendingVerification] = useState(false);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchProfile();
      checkEmailVerification();
    } else if (status === 'unauthenticated') {
      // Redirect or show message if not logged in
      // For now, just stop loading and show nothing or a message
      setIsLoading(false);
      // router.push('/login'); // Example redirect
    }
  }, [status]);

  const fetchProfile = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/profile');
      if (!res.ok) {
        throw new Error(`Failed to fetch profile: ${res.statusText} (status: ${res.status})`);
      }
      const response = await res.json();
      // Handle both direct profile data and nested data response formats
      const data: ProfileData = response.data || response;
      setProfile(data);
    } catch (err: unknown) {
      console.error(err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to load profile.');
      } else {
        setError('An unknown error occurred while loading profile.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const checkEmailVerification = async () => {
    try {
      const res = await fetch('/api/auth/verification-status');
      if (res.ok) {
        const response = await res.json();
        const data = response.data || response;
        setEmailVerified(data.isVerified || data.verified);
      }
    } catch (err) {
      console.error('Failed to check email verification status:', err);
    }
  };

  const handleResendVerification = async () => {
    setIsResendingVerification(true);
    try {
      const res = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: session?.user?.email }),
      });
      if (res.ok) {
        setError(null);
        // Show success message - you could add a success state here
      } else {
        const errorData = await res.json();
        setError(errorData.message || 'Failed to resend verification email');
      }
    } catch (err) {
      setError('Failed to resend verification email');
    } finally {
      setIsResendingVerification(false);
    }
  };

  const handleSave = async (formData: ProfileData): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });
      if (!res.ok) {
        // Try to parse error message from response, otherwise use statusText
        let errorMessage = `Failed to update profile: ${res.statusText} (status: ${res.status})`;
        try {
          const errorData = await res.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // Ignore if response is not JSON or parsing fails, variable 'e' is not needed
        }
        throw new Error(errorMessage);
      }
      const response = await res.json();
      // Handle both direct profile data and nested data response formats
      const updatedProfile: ProfileData = response.data || response;
      setProfile(updatedProfile);
      return { success: true, message: response.message || 'Profile updated successfully!' };
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred while saving profile.';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'loading' || isLoading) {
    return (
      <PageLayout>
        <FormLoadingState message="Loading your profile..." />
      </PageLayout>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <PageLayout>
        <div className="max-w-md mx-auto text-center py-12">
          <h2 className="text-2xl font-semibold mb-4">Authentication Required</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Please log in to view and manage your profile.
          </p>
          <Button asChild>
            <a href="/login">Sign In</a>
          </Button>
        </div>
      </PageLayout>
    );
  }

  if (error) {
    return (
      <PageLayout>
        <PageErrorState
          title="Failed to Load Profile"
          message={error}
          onRetry={fetchProfile}
          showRetry={true}
        />
      </PageLayout>
    );
  }

  if (!profile) {
    return <p>No profile data found.</p>;
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Your Profile</h1>
        {session?.user?.email && (
          <div className="space-y-2">
            <p className="text-gray-600 dark:text-gray-400">Email: {session.user.email}</p>

            {/* Email Verification Status */}
            {emailVerified !== null && (
              <Alert className={`${emailVerified ? 'border-green-500 bg-green-50 dark:bg-green-900/20' : 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'}`}>
                <div className="flex items-center space-x-2">
                  {emailVerified ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-yellow-600" />
                  )}
                  <AlertDescription className={emailVerified ? 'text-green-800 dark:text-green-200' : 'text-yellow-800 dark:text-yellow-200'}>
                    {emailVerified ? (
                      'Your email address is verified.'
                    ) : (
                      <div className="flex items-center justify-between w-full">
                        <span>Your email address is not verified. Please check your inbox.</span>
                        <Button
                          onClick={handleResendVerification}
                          disabled={isResendingVerification}
                          variant="outline"
                          size="sm"
                          className="ml-4"
                        >
                          <Mail className="w-4 h-4 mr-2" />
                          {isResendingVerification ? 'Sending...' : 'Resend'}
                        </Button>
                      </div>
                    )}
                  </AlertDescription>
                </div>
              </Alert>
            )}
          </div>
        )}

        {/* Profile Completion Progress */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6 shadow-sm border">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-semibold">Profile Completion</h2>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {profile.profileCompletionScore || 0}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${profile.profileCompletionScore || 0}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Complete your profile to get better career recommendations and connect with the community.
          </p>
        </div>
      </div>

      <EnhancedProfileForm initialData={profile} onSave={handleSave} />
    </div>
  );
}

// Wrap the profile page with error boundary
export default function ProfilePage() {
  return (
    <PremiumGuard featureName="profile management">
      <ErrorBoundary
        fallback={
          <PageLayout>
            <div className="max-w-4xl mx-auto p-6 text-center">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                <h2 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                  Profile Error
                </h2>
                <p className="text-red-600 dark:text-red-300 mb-4">
                  There was an issue loading your profile. Please try refreshing the page.
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Refresh Page
                </button>
              </div>
            </div>
          </PageLayout>
        }
        onError={(error, errorInfo) => {
          console.error('Profile Page Error:', { error, errorInfo });
        }}
      >
        <ProfilePageContent />
      </ErrorBoundary>
    </PremiumGuard>
  );
}

