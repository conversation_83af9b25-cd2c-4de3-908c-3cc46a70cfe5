import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SubscriptionService } from '@/lib/services/subscriptionService';
import { getSubscriptionStatusForUser } from '@/lib/middleware/subscriptionMiddleware';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';

interface SubscriptionStatusResponse {
  hasAccess: boolean;
  subscription?: {
    id: string;
    tier: string;
    status: string;
    startDate: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
    package: {
      name: string;
      description: string;
      price: number;
    };
  };
  upgradeUrl?: string;
  message?: string;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<SubscriptionStatusResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 30 }, // 30 requests per 15 minutes
    async () => {
      // Check authentication
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      try {
        // Get detailed subscription information
        const subscription = await SubscriptionService.getUserSubscription(session.user.id);
        
        if (!subscription || !subscription.isActive) {
          return NextResponse.json({
            success: true,
            data: {
              hasAccess: false,
              upgradeUrl: '/subscription',
              message: 'Upgrade to access premium features',
            },
          });
        }

        return NextResponse.json({
          success: true,
          data: {
            hasAccess: true,
            subscription: {
              id: subscription.id,
              tier: subscription.tier,
              status: subscription.status,
              startDate: subscription.startDate.toISOString(),
              endDate: subscription.endDate.toISOString(),
              daysRemaining: subscription.daysRemaining,
              isActive: subscription.isActive,
              package: subscription.package,
            },
          },
        });
      } catch (error) {
        console.error('Error getting subscription status:', error);
        
        const apiError = new Error('Failed to get subscription status') as any;
        apiError.statusCode = 500;
        throw apiError;
      }
    }
  );
});

// POST endpoint to refresh subscription status (useful after payment)
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<SubscriptionStatusResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 refreshes per 15 minutes
    async () => {
      // Check authentication
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      try {
        // Force refresh subscription status
        const subscription = await SubscriptionService.getUserSubscription(session.user.id);
        
        if (!subscription || !subscription.isActive) {
          return NextResponse.json({
            success: true,
            data: {
              hasAccess: false,
              upgradeUrl: '/subscription',
              message: 'No active subscription found',
            },
          });
        }

        return NextResponse.json({
          success: true,
          data: {
            hasAccess: true,
            subscription: {
              id: subscription.id,
              tier: subscription.tier,
              status: subscription.status,
              startDate: subscription.startDate.toISOString(),
              endDate: subscription.endDate.toISOString(),
              daysRemaining: subscription.daysRemaining,
              isActive: subscription.isActive,
              package: subscription.package,
            },
            message: 'Subscription status refreshed',
          },
        });
      } catch (error) {
        console.error('Error refreshing subscription status:', error);
        
        const apiError = new Error('Failed to refresh subscription status') as any;
        apiError.statusCode = 500;
        throw apiError;
      }
    }
  );
});
