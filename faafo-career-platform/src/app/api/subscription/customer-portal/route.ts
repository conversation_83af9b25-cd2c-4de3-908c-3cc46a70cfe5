import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { prisma } from '@/lib/prisma';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

interface CustomerPortalResponse {
  url: string;
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<CustomerPortalResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 portal sessions per 15 minutes
      async () => {
        // Check authentication
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
          const error = new Error('Authentication required') as any;
          error.statusCode = 401;
          throw error;
        }

        try {
          // Get user's Stripe customer ID
          const user = await prisma.user.findUnique({
            where: { id: session.user.id },
            select: {
              stripeCustomerId: true,
              email: true,
            },
          });

          if (!user) {
            const error = new Error('User not found') as any;
            error.statusCode = 404;
            throw error;
          }

          if (!user.stripeCustomerId) {
            const error = new Error('No Stripe customer found. Please make a purchase first.') as any;
            error.statusCode = 400;
            throw error;
          }

          // Get return URL from request or use default
          const body = await request.json().catch(() => ({}));
          const returnUrl = body.returnUrl || `${process.env.NEXTAUTH_URL}/subscription/manage`;

          // Create Stripe Customer Portal session
          const portalSession = await stripe.billingPortal.sessions.create({
            customer: user.stripeCustomerId,
            return_url: returnUrl,
          });

          console.log(`🔗 Customer portal session created for user ${session.user.id}`);

          return NextResponse.json({
            success: true,
            data: {
              url: portalSession.url,
            },
          });
        } catch (error: any) {
          console.error('Error creating customer portal session:', error);
          
          // Handle specific Stripe errors
          if (error.type === 'StripeInvalidRequestError') {
            const apiError = new Error('Invalid customer or configuration') as any;
            apiError.statusCode = 400;
            throw apiError;
          }
          
          const apiError = new Error('Failed to create customer portal session') as any;
          apiError.statusCode = 500;
          throw apiError;
        }
      }
    );
  });
});

// GET endpoint to check if customer portal is available
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      try {
        const user = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: {
            stripeCustomerId: true,
            subscriptionStatus: true,
          },
        });

        if (!user) {
          const error = new Error('User not found') as any;
          error.statusCode = 404;
          throw error;
        }

        const isAvailable = !!user.stripeCustomerId;
        const hasActiveSubscription = user.subscriptionStatus === 'ACTIVE';

        return NextResponse.json({
          success: true,
          data: {
            available: isAvailable,
            hasActiveSubscription,
            message: isAvailable 
              ? 'Customer portal is available' 
              : 'Make a purchase first to access billing management',
          },
        });
      } catch (error) {
        console.error('Error checking customer portal availability:', error);
        
        const apiError = new Error('Failed to check customer portal availability') as any;
        apiError.statusCode = 500;
        throw apiError;
      }
    }
  );
});
