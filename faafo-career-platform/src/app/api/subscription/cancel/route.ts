import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SubscriptionService } from '@/lib/services/subscriptionService';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

const cancelSubscriptionSchema = z.object({
  reason: z.string().optional(),
  feedback: z.string().max(500).optional(),
});

interface CancelSubscriptionResponse {
  success: boolean;
  message: string;
  subscription: {
    id: string;
    status: string;
    cancelledAt: string;
    endDate: string;
  };
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<CancelSubscriptionResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 cancellations per 15 minutes
      async () => {
        // Check authentication
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
          const error = new Error('Authentication required') as any;
          error.statusCode = 401;
          throw error;
        }

        // Parse and validate request body
        const body = await request.json();
        const validation = cancelSubscriptionSchema.safeParse(body);
        
        if (!validation.success) {
          const error = new Error('Invalid request data') as any;
          error.statusCode = 400;
          error.details = validation.error.errors;
          throw error;
        }

        const { reason, feedback } = validation.data;

        try {
          // Get user's current subscription
          const user = await prisma.user.findUnique({
            where: { id: session.user.id },
            select: {
              id: true,
              email: true,
              subscriptionStatus: true,
              subscriptionExpiresAt: true,
              subscriptions: {
                where: { status: 'ACTIVE' },
                orderBy: { createdAt: 'desc' },
                take: 1,
              },
            },
          });

          if (!user) {
            const error = new Error('User not found') as any;
            error.statusCode = 404;
            throw error;
          }

          if (user.subscriptionStatus !== 'ACTIVE' || !user.subscriptions.length) {
            const error = new Error('No active subscription found') as any;
            error.statusCode = 400;
            throw error;
          }

          const subscription = user.subscriptions[0];

          // Cancel the subscription in database
          const cancelledSubscription = await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
              status: 'CANCELLED',
              metadata: {
                ...subscription.metadata,
                cancelledAt: new Date().toISOString(),
                cancelReason: reason,
                cancelFeedback: feedback,
              },
            },
          });

          // Update user status to cancelled but keep access until expiration
          await prisma.user.update({
            where: { id: session.user.id },
            data: {
              subscriptionStatus: 'CANCELLED',
              // Keep subscriptionExpiresAt - user retains access until then
            },
          });

          // Log cancellation for analytics
          console.log(`📊 Subscription cancelled: User ${session.user.id}, Reason: ${reason || 'Not provided'}`);

          return NextResponse.json({
            success: true,
            data: {
              success: true,
              message: 'Subscription cancelled successfully. You will retain access until your current billing period ends.',
              subscription: {
                id: cancelledSubscription.id,
                status: cancelledSubscription.status,
                cancelledAt: new Date().toISOString(),
                endDate: cancelledSubscription.endDate.toISOString(),
              },
            },
          });
        } catch (error) {
          console.error('Error cancelling subscription:', error);
          
          const apiError = new Error('Failed to cancel subscription') as any;
          apiError.statusCode = 500;
          throw apiError;
        }
      }
    );
  });
});

// GET endpoint to check if subscription can be cancelled
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      try {
        const subscription = await SubscriptionService.getUserSubscription(session.user.id);
        
        if (!subscription || !subscription.isActive) {
          return NextResponse.json({
            success: true,
            data: {
              canCancel: false,
              reason: 'No active subscription found',
            },
          });
        }

        return NextResponse.json({
          success: true,
          data: {
            canCancel: true,
            subscription: {
              id: subscription.id,
              tier: subscription.tier,
              endDate: subscription.endDate.toISOString(),
              daysRemaining: subscription.daysRemaining,
            },
          },
        });
      } catch (error) {
        console.error('Error checking cancellation eligibility:', error);
        
        const apiError = new Error('Failed to check cancellation eligibility') as any;
        apiError.statusCode = 500;
        throw apiError;
      }
    }
  );
});
