import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { StripeService, SUBSCRIPTION_PACKAGES } from '@/lib/services/stripeService';
import { SubscriptionService } from '@/lib/services/subscriptionService';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

const upgradeSubscriptionSchema = z.object({
  newPackageId: z.enum(['quick-start', 'career-transition', 'career-partner']),
});

interface UpgradeResponse {
  requiresPayment: boolean;
  clientSecret?: string;
  paymentIntentId?: string;
  amount?: number;
  prorationAmount?: number;
  newPackage: {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
  };
  message: string;
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<UpgradeResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 upgrades per 15 minutes
      async () => {
        // Check authentication
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
          const error = new Error('Authentication required') as any;
          error.statusCode = 401;
          throw error;
        }

        // Parse and validate request body
        const body = await request.json();
        const validation = upgradeSubscriptionSchema.safeParse(body);
        
        if (!validation.success) {
          const error = new Error('Invalid request data') as any;
          error.statusCode = 400;
          error.details = validation.error.errors;
          throw error;
        }

        const { newPackageId } = validation.data;

        try {
          // Get current subscription
          const currentSubscription = await SubscriptionService.getUserSubscription(session.user.id);
          
          if (!currentSubscription || !currentSubscription.isActive) {
            const error = new Error('No active subscription found') as any;
            error.statusCode = 400;
            throw error;
          }

          // Get new package details
          const newPackage = StripeService.getPackage(newPackageId);
          if (!newPackage) {
            const error = new Error('Invalid package ID') as any;
            error.statusCode = 400;
            throw error;
          }

          // Get current package details
          const currentPackage = SUBSCRIPTION_PACKAGES.find(p => p.tier === currentSubscription.tier);
          if (!currentPackage) {
            const error = new Error('Current package not found') as any;
            error.statusCode = 500;
            throw error;
          }

          // Check if it's actually an upgrade/downgrade
          if (currentPackage.id === newPackageId) {
            const error = new Error('You are already on this plan') as any;
            error.statusCode = 400;
            throw error;
          }

          // Calculate proration
          const daysRemaining = currentSubscription.daysRemaining;
          const dailyRateOld = currentPackage.price / currentPackage.duration;
          const dailyRateNew = newPackage.price / newPackage.duration;
          
          // Calculate remaining value of current subscription
          const remainingValue = Math.round(dailyRateOld * daysRemaining);
          
          // Calculate new subscription cost for remaining period
          const newCostForRemainingPeriod = Math.round(dailyRateNew * daysRemaining);
          
          // Calculate proration amount (what user needs to pay/receive)
          const prorationAmount = newCostForRemainingPeriod - remainingValue;

          // Get user details
          const user = await prisma.user.findUnique({
            where: { id: session.user.id },
            select: {
              stripeCustomerId: true,
              email: true,
            },
          });

          if (!user?.stripeCustomerId) {
            const error = new Error('Stripe customer not found') as any;
            error.statusCode = 400;
            throw error;
          }

          let response: UpgradeResponse;

          if (prorationAmount > 0) {
            // User needs to pay additional amount
            const paymentIntent = await StripeService.createPaymentIntent(
              user.stripeCustomerId,
              newPackageId,
              {
                userId: session.user.id,
                userEmail: user.email!,
                upgradeFrom: currentPackage.id,
                prorationAmount: prorationAmount.toString(),
                daysRemaining: daysRemaining.toString(),
              }
            );

            // Update payment intent amount to proration amount
            await StripeService.updatePaymentIntent(paymentIntent.id, {
              amount: prorationAmount,
              description: `Upgrade from ${currentPackage.name} to ${newPackage.name} (prorated)`,
            });

            response = {
              requiresPayment: true,
              clientSecret: paymentIntent.client_secret!,
              paymentIntentId: paymentIntent.id,
              amount: prorationAmount,
              prorationAmount,
              newPackage: {
                id: newPackage.id,
                name: newPackage.name,
                description: newPackage.description,
                price: newPackage.price,
                duration: newPackage.duration,
              },
              message: `Upgrade requires additional payment of $${(prorationAmount / 100).toFixed(2)}`,
            };
          } else {
            // Downgrade or no additional payment needed
            // Process immediately
            await processSubscriptionChange(session.user.id, currentSubscription.id, newPackage, prorationAmount);

            response = {
              requiresPayment: false,
              prorationAmount,
              newPackage: {
                id: newPackage.id,
                name: newPackage.name,
                description: newPackage.description,
                price: newPackage.price,
                duration: newPackage.duration,
              },
              message: prorationAmount < 0 
                ? `Downgraded successfully. Credit of $${Math.abs(prorationAmount / 100).toFixed(2)} will be applied to your next billing cycle.`
                : 'Subscription updated successfully.',
            };
          }

          return NextResponse.json({
            success: true,
            data: response,
          });
        } catch (error) {
          console.error('Error processing subscription upgrade:', error);
          
          const apiError = new Error('Failed to process subscription upgrade') as any;
          apiError.statusCode = 500;
          throw apiError;
        }
      }
    );
  });
});

async function processSubscriptionChange(
  userId: string, 
  currentSubscriptionId: string, 
  newPackage: any, 
  prorationAmount: number
) {
  // End current subscription
  await prisma.subscription.update({
    where: { id: currentSubscriptionId },
    data: {
      status: 'CANCELLED',
      metadata: {
        cancelledAt: new Date().toISOString(),
        reason: 'upgraded',
      },
    },
  });

  // Create new subscription with same end date
  const currentSub = await prisma.subscription.findUnique({
    where: { id: currentSubscriptionId },
    select: { endDate: true },
  });

  const newSubscription = await prisma.subscription.create({
    data: {
      userId,
      stripeProductId: newPackage.productId,
      tier: newPackage.tier,
      status: 'ACTIVE',
      startDate: new Date(),
      endDate: currentSub!.endDate, // Keep same end date
      autoRenew: false,
      metadata: {
        packageId: newPackage.id,
        packageName: newPackage.name,
        duration: newPackage.duration,
        prorationAmount,
        upgradedFrom: currentSubscriptionId,
      },
    },
  });

  // Update user
  await prisma.user.update({
    where: { id: userId },
    data: {
      subscriptionTier: newPackage.tier,
    },
  });

  return newSubscription;
}

// GET endpoint to get upgrade options
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      try {
        const currentSubscription = await SubscriptionService.getUserSubscription(session.user.id);
        
        if (!currentSubscription || !currentSubscription.isActive) {
          return NextResponse.json({
            success: true,
            data: {
              hasActiveSubscription: false,
              message: 'No active subscription found',
            },
          });
        }

        // Get available upgrade/downgrade options
        const currentPackage = SUBSCRIPTION_PACKAGES.find(p => p.tier === currentSubscription.tier);
        const otherPackages = SUBSCRIPTION_PACKAGES.filter(p => p.tier !== currentSubscription.tier);

        const options = otherPackages.map(pkg => {
          const dailyRateOld = currentPackage!.price / currentPackage!.duration;
          const dailyRateNew = pkg.price / pkg.duration;
          const remainingValue = Math.round(dailyRateOld * currentSubscription.daysRemaining);
          const newCostForRemainingPeriod = Math.round(dailyRateNew * currentSubscription.daysRemaining);
          const prorationAmount = newCostForRemainingPeriod - remainingValue;

          return {
            packageId: pkg.id,
            name: pkg.name,
            description: pkg.description,
            price: pkg.price,
            duration: pkg.duration,
            prorationAmount,
            isUpgrade: prorationAmount > 0,
            formattedProration: prorationAmount > 0 
              ? `+$${(prorationAmount / 100).toFixed(2)}` 
              : prorationAmount < 0 
                ? `-$${Math.abs(prorationAmount / 100).toFixed(2)}` 
                : '$0.00',
          };
        });

        return NextResponse.json({
          success: true,
          data: {
            hasActiveSubscription: true,
            currentPackage: {
              id: currentPackage!.id,
              name: currentPackage!.name,
              tier: currentSubscription.tier,
            },
            daysRemaining: currentSubscription.daysRemaining,
            upgradeOptions: options,
          },
        });
      } catch (error) {
        console.error('Error getting upgrade options:', error);
        
        const apiError = new Error('Failed to get upgrade options') as any;
        apiError.statusCode = 500;
        throw apiError;
      }
    }
  );
});
