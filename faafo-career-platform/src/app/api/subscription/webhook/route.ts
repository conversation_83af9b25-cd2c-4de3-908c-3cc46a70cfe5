import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { SubscriptionService } from '@/lib/services/subscriptionService';
import { StripeService } from '@/lib/services/stripeService';
import { prisma } from '@/lib/prisma';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature');

  if (!signature) {
    console.error('Missing Stripe signature');
    return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
  }

  let event: Stripe.Event;

  try {
    // Verify webhook signature
    if (endpointSecret) {
      event = stripe.webhooks.constructEvent(body, signature, endpointSecret);
    } else {
      // For development - parse without verification
      event = JSON.parse(body);
      console.warn('⚠️  Webhook signature verification disabled (development mode)');
    }
  } catch (err: any) {
    console.error('Webhook signature verification failed:', err.message);
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
  }

  console.log(`🔔 Received webhook: ${event.type}`);

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'customer.created':
        await handleCustomerCreated(event.data.object as Stripe.Customer);
        break;

      case 'customer.updated':
        await handleCustomerUpdated(event.data.object as Stripe.Customer);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('💰 Payment succeeded:', paymentIntent.id);

  const userId = paymentIntent.metadata?.userId;
  const packageId = paymentIntent.metadata?.packageId;

  if (!userId || !packageId) {
    console.error('Missing required metadata in payment intent:', {
      userId,
      packageId,
      metadata: paymentIntent.metadata,
    });
    return;
  }

  try {
    // Create subscription
    await SubscriptionService.createSubscription({
      userId,
      packageId,
      stripePaymentIntentId: paymentIntent.id,
      stripeCustomerId: paymentIntent.customer as string,
    });

    console.log(`✅ Subscription created for user ${userId} with package ${packageId}`);
  } catch (error) {
    console.error('Failed to create subscription:', error);
    
    // Update payment record to indicate subscription creation failed
    await prisma.payment.updateMany({
      where: { stripePaymentIntentId: paymentIntent.id },
      data: {
        metadata: {
          error: 'Failed to create subscription',
          originalMetadata: paymentIntent.metadata,
        },
      },
    });
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('❌ Payment failed:', paymentIntent.id);

  const userId = paymentIntent.metadata?.userId;

  if (!userId) {
    console.error('Missing userId in failed payment intent metadata');
    return;
  }

  try {
    // Create failed payment record
    await prisma.payment.create({
      data: {
        userId,
        stripePaymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: 'FAILED',
        description: 'Payment failed',
        metadata: paymentIntent.metadata,
      },
    });

    console.log(`📝 Failed payment recorded for user ${userId}`);
  } catch (error) {
    console.error('Failed to record failed payment:', error);
  }
}

async function handleCustomerCreated(customer: Stripe.Customer) {
  console.log('👤 Customer created:', customer.id);
  
  // Update user record with Stripe customer ID if we can find them by email
  if (customer.email) {
    try {
      await prisma.user.updateMany({
        where: { 
          email: customer.email,
          stripeCustomerId: null,
        },
        data: { stripeCustomerId: customer.id },
      });
      
      console.log(`✅ Updated user with email ${customer.email} with Stripe customer ID`);
    } catch (error) {
      console.error('Failed to update user with Stripe customer ID:', error);
    }
  }
}

async function handleCustomerUpdated(customer: Stripe.Customer) {
  console.log('👤 Customer updated:', customer.id);
  
  // Update user record if email changed
  if (customer.email) {
    try {
      const user = await prisma.user.findFirst({
        where: { stripeCustomerId: customer.id },
      });
      
      if (user && user.email !== customer.email) {
        await prisma.user.update({
          where: { id: user.id },
          data: { email: customer.email },
        });
        
        console.log(`✅ Updated user email to ${customer.email}`);
      }
    } catch (error) {
      console.error('Failed to update user email:', error);
    }
  }
}

// Disable body parsing for webhooks
export const runtime = 'nodejs';
export const preferredRegion = 'auto';
