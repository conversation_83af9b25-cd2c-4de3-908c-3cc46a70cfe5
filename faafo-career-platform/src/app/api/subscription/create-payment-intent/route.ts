import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { StripeService } from '@/lib/services/stripeService';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

const createPaymentIntentSchema = z.object({
  packageId: z.enum(['quick-start', 'career-transition', 'career-partner']),
});

interface CreatePaymentIntentResponse {
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
  package: {
    id: string;
    name: string;
    description: string;
    price: number;
    duration: number;
  };
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<CreatePaymentIntentResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 payment intents per 15 minutes
      async () => {
        // Check authentication
        const session = await getServerSession(authOptions);
        if (!session?.user?.id) {
          const error = new Error('Authentication required') as any;
          error.statusCode = 401;
          throw error;
        }

        // Parse and validate request body
        const body = await request.json();
        const validation = createPaymentIntentSchema.safeParse(body);
        
        if (!validation.success) {
          const error = new Error('Invalid request data') as any;
          error.statusCode = 400;
          error.details = validation.error.errors;
          throw error;
        }

        const { packageId } = validation.data;

        // Get package details
        const package_ = StripeService.getPackage(packageId);
        if (!package_) {
          const error = new Error('Invalid package ID') as any;
          error.statusCode = 400;
          throw error;
        }

        // Check if user already has active subscription
        const user = await prisma.user.findUnique({
          where: { id: session.user.id },
          select: {
            stripeCustomerId: true,
            subscriptionStatus: true,
            subscriptionExpiresAt: true,
          },
        });

        if (user?.subscriptionStatus === 'ACTIVE' && user.subscriptionExpiresAt && user.subscriptionExpiresAt > new Date()) {
          const error = new Error('You already have an active subscription') as any;
          error.statusCode = 409;
          throw error;
        }

        // Create or get Stripe customer
        let stripeCustomerId = user?.stripeCustomerId;
        
        if (!stripeCustomerId) {
          const stripeCustomer = await StripeService.createCustomer(
            session.user.email!,
            session.user.name || undefined
          );
          stripeCustomerId = stripeCustomer.id;

          // Update user with Stripe customer ID
          await prisma.user.update({
            where: { id: session.user.id },
            data: { stripeCustomerId },
          });
        }

        // Create payment intent
        const paymentIntent = await StripeService.createPaymentIntent(
          stripeCustomerId,
          packageId,
          {
            userId: session.user.id,
            userEmail: session.user.email!,
          }
        );

        return NextResponse.json({
          success: true,
          data: {
            clientSecret: paymentIntent.client_secret!,
            paymentIntentId: paymentIntent.id,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            package: {
              id: packageId,
              name: package_.name,
              description: package_.description,
              price: package_.price,
              duration: package_.duration,
            },
          },
        });
      }
    );
  });
});

// GET endpoint to retrieve payment intent status
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const { searchParams } = new URL(request.url);
      const paymentIntentId = searchParams.get('payment_intent_id');

      if (!paymentIntentId) {
        const error = new Error('Payment intent ID required') as any;
        error.statusCode = 400;
        throw error;
      }

      try {
        const paymentIntent = await StripeService.getPaymentIntent(paymentIntentId);
        
        // Verify this payment intent belongs to the current user
        if (paymentIntent.metadata?.userId !== session.user.id) {
          const error = new Error('Payment intent not found') as any;
          error.statusCode = 404;
          throw error;
        }

        return NextResponse.json({
          success: true,
          data: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            metadata: paymentIntent.metadata,
          },
        });
      } catch (stripeError: any) {
        console.error('Stripe error:', stripeError);
        const error = new Error('Failed to retrieve payment intent') as any;
        error.statusCode = 500;
        throw error;
      }
    }
  );
});
