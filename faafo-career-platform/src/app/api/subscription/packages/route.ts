import { NextRequest, NextResponse } from 'next/server';
import { StripeService, SUBSCRIPTION_PACKAGES } from '@/lib/services/stripeService';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';

interface PackageResponse {
  id: string;
  name: string;
  description: string;
  price: number; // in cents
  priceFormatted: string; // formatted price like "$49.00"
  duration: number; // in days
  durationFormatted: string; // formatted duration like "30 days"
  tier: string;
  features: string[];
  popular?: boolean;
  savings?: string;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<PackageResponse[]>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 requests per 15 minutes
    async () => {
      try {
        const packages = SUBSCRIPTION_PACKAGES.map((pkg): PackageResponse => {
          // Calculate formatted price
          const priceFormatted = `$${(pkg.price / 100).toFixed(2)}`;
          
          // Calculate formatted duration
          let durationFormatted: string;
          if (pkg.duration === 30) {
            durationFormatted = '30 days';
          } else if (pkg.duration === 90) {
            durationFormatted = '3 months';
          } else if (pkg.duration === 365) {
            durationFormatted = '1 year';
          } else {
            durationFormatted = `${pkg.duration} days`;
          }

          // Define features for each package
          let features: string[];
          let popular = false;
          let savings: string | undefined;

          switch (pkg.tier) {
            case 'QUICK_START':
              features = [
                'AI-powered career assessment',
                'Basic resume builder',
                'Interview practice (limited)',
                'Career path recommendations',
                '30-day access',
                'Email support',
              ];
              break;

            case 'CAREER_TRANSITION':
              features = [
                'Everything in Quick Start',
                'Advanced skill gap analysis',
                'Unlimited AI features',
                'Premium resume templates',
                'Unlimited interview practice',
                'Personalized learning paths',
                '90-day access',
                'Priority email support',
              ];
              popular = true; // Mark as most popular
              savings = 'Best Value';
              break;

            case 'CAREER_PARTNER':
              features = [
                'Everything in Career Transition',
                'Full year access',
                'Advanced analytics & insights',
                'Career coaching resources',
                'Industry-specific guidance',
                'Networking recommendations',
                'Priority support',
                'Early access to new features',
              ];
              savings = 'Best for Long-term Growth';
              break;

            default:
              features = [];
          }

          return {
            id: pkg.id,
            name: pkg.name,
            description: pkg.description,
            price: pkg.price,
            priceFormatted,
            duration: pkg.duration,
            durationFormatted,
            tier: pkg.tier,
            features,
            popular,
            savings,
          };
        });

        return NextResponse.json({
          success: true,
          data: packages,
        });
      } catch (error) {
        console.error('Error getting subscription packages:', error);
        
        const apiError = new Error('Failed to get subscription packages') as any;
        apiError.statusCode = 500;
        throw apiError;
      }
    }
  );
});
