'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, CreditCard, Shield, Lock } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

// Initialize Stripe
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ||
  'pk_test_51R0gt709MovY3jfspBFxOMhfYLTX0TIp9NjtmZC3N2jeBDm37SA6ieJ7F0jl1pDv7ZBnOvVlgEdrwQgXIEgg6QIJ00ooZwTO2a'
);

interface Package {
  id: string;
  name: string;
  description: string;
  price: number;
  priceFormatted: string;
  duration: number;
  durationFormatted: string;
  features: string[];
}

function CheckoutForm({ selectedPackage }: { selectedPackage: Package }) {
  const stripe = useStripe();
  const elements = useElements();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  useEffect(() => {
    createPaymentIntent();
  }, [selectedPackage]);

  const createPaymentIntent = async () => {
    try {
      const response = await fetch('/api/subscription/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageId: selectedPackage.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment intent');
      }

      const data = await response.json();
      setClientSecret(data.data.clientSecret);
    } catch (err: any) {
      console.error('Error creating payment intent:', err);
      setError(err.message || 'Failed to initialize payment');
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!stripe || !elements || !clientSecret) {
      return;
    }

    setLoading(true);
    setError(null);

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setError('Card element not found');
      setLoading(false);
      return;
    }

    try {
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        },
      });

      if (stripeError) {
        setError(stripeError.message || 'Payment failed');
      } else if (paymentIntent?.status === 'succeeded') {
        // Payment successful - redirect to success page
        router.push('/subscription/success?payment_intent=' + paymentIntent.id);
      }
    } catch (err: any) {
      console.error('Payment error:', err);
      setError('Payment processing failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
    },
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Card Information
        </label>
        <div className="border border-gray-300 rounded-md p-3 bg-white">
          <CardElement options={cardElementOptions} />
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center text-sm text-gray-600 mb-2">
          <Shield className="h-4 w-4 mr-2" />
          Your payment information is secure and encrypted
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <Lock className="h-4 w-4 mr-2" />
          Protected by Stripe's industry-leading security
        </div>
      </div>

      <Button 
        type="submit" 
        disabled={!stripe || loading || !clientSecret}
        className="w-full"
        size="lg"
      >
        {loading ? (
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Processing...
          </div>
        ) : (
          <>
            <CreditCard className="h-4 w-4 mr-2" />
            Pay {selectedPackage.priceFormatted}
          </>
        )}
      </Button>
    </form>
  );
}

function CheckoutContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const packageId = searchParams.get('package');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin?callbackUrl=/subscription');
      return;
    }

    if (!packageId) {
      router.push('/subscription');
      return;
    }

    fetchPackage();
  }, [session, status, packageId, router]);

  const fetchPackage = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/subscription/packages');
      if (!response.ok) {
        throw new Error('Failed to fetch packages');
      }

      const data = await response.json();
      const packages = data.data || [];
      const pkg = packages.find((p: Package) => p.id === packageId);
      
      if (!pkg) {
        throw new Error('Package not found');
      }

      setSelectedPackage(pkg);
    } catch (err: any) {
      console.error('Error fetching package:', err);
      setError(err.message || 'Failed to load package information');
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading checkout...</p>
        </div>
      </div>
    );
  }

  if (error || !selectedPackage) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-red-600">{error || 'Package not found'}</p>
          <Button onClick={() => router.push('/subscription')} className="mt-4">
            Back to Packages
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/subscription')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Packages
          </Button>
          <h1 className="text-3xl font-bold text-gray-900">Complete Your Purchase</h1>
          <p className="text-gray-600 mt-2">Secure checkout powered by Stripe</p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{selectedPackage.name}</h3>
                    <p className="text-sm text-gray-600">{selectedPackage.durationFormatted} access</p>
                  </div>
                  <span className="font-semibold">{selectedPackage.priceFormatted}</span>
                </div>
                
                <div className="border-t pt-4">
                  <h4 className="font-medium mb-2">What's included:</h4>
                  <ul className="space-y-1">
                    {selectedPackage.features.slice(0, 5).map((feature, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-center">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                        {feature}
                      </li>
                    ))}
                    {selectedPackage.features.length > 5 && (
                      <li className="text-sm text-gray-500">
                        +{selectedPackage.features.length - 5} more features
                      </li>
                    )}
                  </ul>
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center font-semibold text-lg">
                    <span>Total</span>
                    <span>{selectedPackage.priceFormatted}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
                <CardDescription>
                  Enter your payment details to complete your purchase
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Elements stripe={stripePromise}>
                  <CheckoutForm selectedPackage={selectedPackage} />
                </Elements>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <CheckoutContent />
    </Suspense>
  );
}
