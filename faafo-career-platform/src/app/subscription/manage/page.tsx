'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Calendar, 
  Crown, 
  ExternalLink, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  ArrowLeft,
  Loader2
} from 'lucide-react';

interface SubscriptionDetails {
  hasAccess: boolean;
  subscription?: {
    id: string;
    tier: string;
    status: string;
    startDate: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
    package: {
      name: string;
      description: string;
      price: number;
    };
  };
}

interface CancellationInfo {
  canCancel: boolean;
  reason?: string;
  subscription?: {
    id: string;
    tier: string;
    endDate: string;
    daysRemaining: number;
  };
}

export default function SubscriptionManagePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails | null>(null);
  const [cancellationInfo, setCancellationInfo] = useState<CancellationInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin?callbackUrl=/subscription/manage');
      return;
    }

    fetchSubscriptionDetails();
  }, [session, status, router]);

  const fetchSubscriptionDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch subscription status
      const statusResponse = await fetch('/api/subscription/status');
      if (!statusResponse.ok) {
        throw new Error('Failed to fetch subscription details');
      }
      const statusData = await statusResponse.json();
      setSubscriptionDetails(statusData.data);

      // Fetch cancellation info if user has active subscription
      if (statusData.data.hasAccess) {
        const cancelResponse = await fetch('/api/subscription/cancel');
        if (cancelResponse.ok) {
          const cancelData = await cancelResponse.json();
          setCancellationInfo(cancelData.data);
        }
      }
    } catch (err: any) {
      console.error('Error fetching subscription details:', err);
      setError(err.message || 'Failed to load subscription details');
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerPortal = async () => {
    try {
      setActionLoading(true);
      
      const response = await fetch('/api/subscription/customer-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          returnUrl: window.location.href,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to open customer portal');
      }

      const data = await response.json();
      window.location.href = data.data.url;
    } catch (err: any) {
      console.error('Error opening customer portal:', err);
      setError(err.message || 'Failed to open billing portal');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      setActionLoading(true);
      
      const response = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: 'user_requested',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel subscription');
      }

      const data = await response.json();
      
      // Refresh subscription details
      await fetchSubscriptionDetails();
      setShowCancelConfirm(false);
      
      // Show success message
      setError(null);
    } catch (err: any) {
      console.error('Error cancelling subscription:', err);
      setError(err.message || 'Failed to cancel subscription');
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (cents: number) => {
    return `$${(cents / 100).toFixed(2)}`;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
      case 'CANCELLED':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Cancelled</Badge>;
      case 'EXPIRED':
        return <Badge variant="secondary"><AlertTriangle className="h-3 w-3 mr-1" />Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-4xl mx-auto text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-700">Loading subscription details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Button 
            variant="ghost" 
            onClick={() => router.push('/dashboard')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Subscription Management</h1>
          <p className="text-gray-700 dark:text-gray-300 mt-2">Manage your FAAFO Career Platform subscription</p>
        </div>

        {error && (
          <Alert className="mb-6" variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Current Subscription */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Crown className="h-5 w-5 mr-2 text-blue-600" />
              Current Subscription
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!subscriptionDetails?.hasAccess ? (
              <div className="text-center py-8">
                <p className="text-gray-700 dark:text-gray-300 mb-4">You don't have an active subscription</p>
                <Button onClick={() => router.push('/subscription')}>
                  View Available Plans
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">{subscriptionDetails.subscription?.package.name}</h3>
                    <p className="text-gray-700 dark:text-gray-300">{subscriptionDetails.subscription?.package.description}</p>
                  </div>
                  {getStatusBadge(subscriptionDetails.subscription?.status || '')}
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">Price</p>
                    <p className="font-semibold">{formatPrice(subscriptionDetails.subscription?.package.price || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">Start Date</p>
                    <p className="font-semibold">{formatDate(subscriptionDetails.subscription?.startDate || '')}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">End Date</p>
                    <p className="font-semibold">{formatDate(subscriptionDetails.subscription?.endDate || '')}</p>
                  </div>
                </div>

                {subscriptionDetails.subscription?.daysRemaining !== undefined && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                      <span className="text-blue-800">
                        {subscriptionDetails.subscription.daysRemaining > 0 
                          ? `${subscriptionDetails.subscription.daysRemaining} days remaining`
                          : 'Subscription has expired'
                        }
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Management Actions */}
        {subscriptionDetails?.hasAccess && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Manage Subscription</CardTitle>
              <CardDescription>
                Update your billing information, download invoices, or make changes to your subscription
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  onClick={handleCustomerPortal}
                  disabled={actionLoading}
                  className="w-full sm:w-auto"
                >
                  {actionLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <CreditCard className="h-4 w-4 mr-2" />
                  )}
                  Open Billing Portal
                  <ExternalLink className="h-4 w-4 ml-2" />
                </Button>
                
                {cancellationInfo?.canCancel && subscriptionDetails.subscription?.status === 'ACTIVE' && (
                  <div>
                    <Separator className="my-4" />
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">Cancel Subscription</h4>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        You can cancel your subscription at any time. You'll retain access until your current billing period ends.
                      </p>
                      {!showCancelConfirm ? (
                        <Button 
                          variant="outline" 
                          onClick={() => setShowCancelConfirm(true)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          Cancel Subscription
                        </Button>
                      ) : (
                        <div className="bg-red-50 p-4 rounded-lg space-y-3">
                          <p className="text-red-800 font-medium">Are you sure you want to cancel?</p>
                          <p className="text-red-700 text-sm">
                            Your subscription will be cancelled, but you'll keep access until {formatDate(subscriptionDetails.subscription?.endDate || '')}.
                          </p>
                          <div className="flex space-x-2">
                            <Button 
                              variant="destructive" 
                              size="sm"
                              onClick={handleCancelSubscription}
                              disabled={actionLoading}
                            >
                              {actionLoading ? (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              ) : null}
                              Yes, Cancel
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setShowCancelConfirm(false)}
                            >
                              Keep Subscription
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Upgrade Options */}
        {!subscriptionDetails?.hasAccess && (
          <Card>
            <CardHeader>
              <CardTitle>Upgrade to Premium</CardTitle>
              <CardDescription>
                Get access to all AI-powered career tools and features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => router.push('/subscription')} className="w-full sm:w-auto">
                <Crown className="h-4 w-4 mr-2" />
                View Subscription Plans
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
