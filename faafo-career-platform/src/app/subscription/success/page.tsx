'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, ArrowRight, Star, Zap } from 'lucide-react';

interface SubscriptionDetails {
  hasAccess: boolean;
  subscription?: {
    id: string;
    tier: string;
    status: string;
    startDate: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
    package: {
      name: string;
      description: string;
      price: number;
    };
  };
}

function SuccessContent() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [subscriptionDetails, setSubscriptionDetails] = useState<SubscriptionDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const paymentIntentId = searchParams.get('payment_intent');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin');
      return;
    }

    if (!paymentIntentId) {
      router.push('/subscription');
      return;
    }

    // Wait a moment for webhook to process, then fetch subscription details
    const timer = setTimeout(() => {
      fetchSubscriptionDetails();
    }, 2000);

    return () => clearTimeout(timer);
  }, [session, status, paymentIntentId, router]);

  const fetchSubscriptionDetails = async () => {
    try {
      setLoading(true);
      
      // Refresh subscription status
      const response = await fetch('/api/subscription/status', {
        method: 'POST', // Use POST to force refresh
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscription details');
      }

      const data = await response.json();
      setSubscriptionDetails(data.data);
    } catch (err: any) {
      console.error('Error fetching subscription details:', err);
      setError(err.message || 'Failed to load subscription details');
    } finally {
      setLoading(false);
    }
  };

  const handleContinueToAssessment = () => {
    router.push('/assessment');
  };

  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Processing Your Payment...
            </h1>
            <p className="text-gray-600">
              Please wait while we confirm your subscription and set up your account.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto text-center">
          <Card>
            <CardContent className="pt-6">
              <div className="text-red-600 mb-4">
                <p>{error}</p>
              </div>
              <div className="space-y-3">
                <Button onClick={fetchSubscriptionDetails} className="w-full">
                  Try Again
                </Button>
                <Button variant="outline" onClick={() => router.push('/subscription')} className="w-full">
                  Back to Subscription
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!subscriptionDetails?.hasAccess) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto text-center">
          <Card>
            <CardContent className="pt-6">
              <div className="text-yellow-600 mb-4">
                <p>Your payment is being processed. This may take a few moments.</p>
              </div>
              <div className="space-y-3">
                <Button onClick={fetchSubscriptionDetails} className="w-full">
                  Check Status
                </Button>
                <Button variant="outline" onClick={() => router.push('/dashboard')} className="w-full">
                  Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const subscription = subscriptionDetails.subscription!;
  const tierName = subscription.tier.replace('_', ' ').toLowerCase()
    .replace(/\b\w/g, l => l.toUpperCase());

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
            <Check className="h-10 w-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome to FAAFO Premium!
          </h1>
          <p className="text-lg text-gray-600">
            Your payment was successful and your subscription is now active.
          </p>
        </div>

        {/* Subscription Details */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Subscription Details</span>
              <Badge className="bg-green-100 text-green-800">
                Active
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Package</p>
                <p className="font-semibold">{tierName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Duration</p>
                <p className="font-semibold">{subscription.daysRemaining} days</p>
              </div>
            </div>
            
            <div>
              <p className="text-sm text-gray-600">Access Until</p>
              <p className="font-semibold">
                {new Date(subscription.endDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* What's Next */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Star className="h-5 w-5 text-yellow-500 mr-2" />
              What's Next?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-blue-600 font-semibold text-sm">1</span>
                </div>
                <div>
                  <h4 className="font-semibold">Complete Your Career Assessment</h4>
                  <p className="text-sm text-gray-600">
                    Get personalized AI-powered insights about your career path and skills.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-blue-600 font-semibold text-sm">2</span>
                </div>
                <div>
                  <h4 className="font-semibold">Explore Your Dashboard</h4>
                  <p className="text-sm text-gray-600">
                    Access all premium features including resume builder, interview practice, and more.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center mr-3 mt-0.5">
                  <span className="text-blue-600 font-semibold text-sm">3</span>
                </div>
                <div>
                  <h4 className="font-semibold">Start Your Learning Journey</h4>
                  <p className="text-sm text-gray-600">
                    Follow your personalized learning path and track your progress.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button onClick={handleContinueToAssessment} className="w-full" size="lg">
            <Zap className="h-4 w-4 mr-2" />
            Complete Your Assessment
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
          
          <Button variant="outline" onClick={handleGoToDashboard} className="w-full" size="lg">
            Explore Dashboard
          </Button>
        </div>

        {/* Support */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Need help getting started?{' '}
            <a href="/support" className="text-blue-600 hover:underline">
              Contact our support team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function SuccessPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SuccessContent />
    </Suspense>
  );
}
