'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Star, Zap, Crown } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

// Initialize Stripe
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ||
  'pk_test_51R0gt709MovY3jfspBFxOMhfYLTX0TIp9NjtmZC3N2jeBDm37SA6ieJ7F0jl1pDv7ZBnOvVlgEdrwQgXIEgg6QIJ00ooZwTO2a'
);

interface Package {
  id: string;
  name: string;
  description: string;
  price: number;
  priceFormatted: string;
  duration: number;
  durationFormatted: string;
  tier: string;
  features: string[];
  popular?: boolean;
  savings?: string;
}

interface SubscriptionStatus {
  hasAccess: boolean;
  subscription?: {
    tier: string;
    status: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
  };
}

export default function SubscriptionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [packages, setPackages] = useState<Package[]>([]);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin?callbackUrl=/subscription');
      return;
    }

    fetchData();
  }, [session, status, router]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch packages and subscription status in parallel
      const [packagesRes, statusRes] = await Promise.all([
        fetch('/api/subscription/packages'),
        fetch('/api/subscription/status'),
      ]);

      if (packagesRes.ok) {
        const packagesData = await packagesRes.json();
        setPackages(packagesData.data || []);
      }

      if (statusRes.ok) {
        const statusData = await statusRes.json();
        setSubscriptionStatus(statusData.data || null);
      }
    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError('Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPackage = (packageId: string) => {
    router.push(`/subscription/checkout?package=${packageId}`);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading subscription options...</p>
        </div>
      </div>
    );
  }

  // Check if Stripe failed to load
  if (!stripePromise) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-red-600">Failed to load payment system. Please refresh the page.</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Refresh Page
          </Button>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchData} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // If user already has active subscription
  if (subscriptionStatus?.hasAccess && subscriptionStatus.subscription?.isActive) {
    return (
      <div className="container mx-auto py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <Check className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-green-800 mb-2">
              You're All Set!
            </h1>
            <p className="text-green-700 mb-4">
              You have an active {subscriptionStatus.subscription.tier.replace('_', ' ')} subscription.
            </p>
            <p className="text-sm text-green-600 mb-6">
              {subscriptionStatus.subscription.daysRemaining} days remaining
            </p>
            <div className="space-y-3">
              <Button onClick={() => router.push('/dashboard')} className="w-full">
                Go to Dashboard
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push('/assessment')}
                className="w-full"
              >
                Continue Assessment
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-white mb-4">
          Choose Your Career Growth Package
        </h1>
        <p className="text-xl text-gray-200 max-w-3xl mx-auto">
          Unlock AI-powered career guidance, personalized recommendations, and comprehensive tools
          to accelerate your professional journey.
        </p>
      </div>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {packages.map((pkg) => (
          <Card 
            key={pkg.id} 
            className={`relative ${pkg.popular ? 'border-blue-500 shadow-lg scale-105' : 'border-gray-200'}`}
          >
            {pkg.popular && (
              <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-600">
                Most Popular
              </Badge>
            )}
            
            <CardHeader className="text-center">
              <div className="flex justify-center mb-2">
                {pkg.tier === 'QUICK_START' && <Zap className="h-8 w-8 text-orange-500" />}
                {pkg.tier === 'CAREER_TRANSITION' && <Star className="h-8 w-8 text-blue-500" />}
                {pkg.tier === 'CAREER_PARTNER' && <Crown className="h-8 w-8 text-purple-500" />}
              </div>
              <CardTitle className="text-2xl">{pkg.name}</CardTitle>
              <CardDescription className="text-sm">{pkg.durationFormatted}</CardDescription>
              <div className="mt-4">
                <span className="text-4xl font-bold">{pkg.priceFormatted}</span>
                <span className="text-gray-500 ml-2">one-time</span>
              </div>
              {pkg.savings && (
                <Badge variant="secondary" className="mt-2">
                  {pkg.savings}
                </Badge>
              )}
            </CardHeader>

            <CardContent>
              <ul className="space-y-3">
                {pkg.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>

            <CardFooter>
              <Button 
                onClick={() => handleSelectPackage(pkg.id)}
                className={`w-full ${pkg.popular ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                size="lg"
              >
                Get Started
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Trust Indicators */}
      <div className="mt-16 text-center">
        <div className="max-w-4xl mx-auto">
          <h3 className="text-lg font-semibold text-white mb-6">
            Why Choose FAAFO Career Platform?
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <div className="bg-blue-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-blue-600" />
              </div>
              <h4 className="font-semibold mb-2 text-white">AI-Powered Insights</h4>
              <p className="text-sm text-gray-300">
                Advanced AI analyzes your skills and provides personalized career recommendations
              </p>
            </div>
            <div>
              <div className="bg-green-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600" />
              </div>
              <h4 className="font-semibold mb-2 text-white">Proven Results</h4>
              <p className="text-sm text-gray-300">
                Thousands of professionals have accelerated their careers with our platform
              </p>
            </div>
            <div>
              <div className="bg-purple-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Crown className="h-8 w-8 text-purple-600" />
              </div>
              <h4 className="font-semibold mb-2 text-white">Expert Support</h4>
              <p className="text-sm text-gray-300">
                Get guidance from career experts and industry professionals
              </p>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}
