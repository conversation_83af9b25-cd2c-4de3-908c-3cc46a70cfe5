'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Shield, Crown, ArrowRight, Loader2 } from 'lucide-react';

interface SubscriptionStatus {
  hasAccess: boolean;
  subscription?: {
    tier: string;
    status: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
  };
  upgradeUrl?: string;
  message?: string;
}

interface PremiumGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  showUpgradePrompt?: boolean;
  featureName?: string;
}

export function PremiumGuard({ 
  children, 
  fallback,
  redirectTo = '/subscription',
  showUpgradePrompt = true,
  featureName = 'this feature'
}: PremiumGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkSubscription = async () => {
      if (status === 'loading') return;
      
      if (!session?.user) {
        // Not authenticated - redirect to login
        router.push(`/login?callbackUrl=${encodeURIComponent(window.location.pathname)}`);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/subscription/status', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.error || 'Failed to check subscription status');
        }

        setSubscriptionStatus(data.data);
      } catch (err) {
        console.error('Error checking subscription status:', err);
        setError(err instanceof Error ? err.message : 'Failed to verify subscription');
        // On error, assume no access for security
        setSubscriptionStatus({ hasAccess: false, message: 'Unable to verify subscription' });
      } finally {
        setLoading(false);
      }
    };

    checkSubscription();
  }, [session, status, router]);

  // Show loading state
  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Verifying access...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md mx-auto text-center p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <Shield className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
              Access Verification Failed
            </h2>
            <p className="text-red-600 dark:text-red-300 mb-4">
              {error}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has premium access
  if (!subscriptionStatus?.hasAccess) {
    // Show custom fallback if provided
    if (fallback) {
      return <>{fallback}</>;
    }

    // Show upgrade prompt if enabled
    if (showUpgradePrompt) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="max-w-md mx-auto text-center p-6">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-8">
              <Crown className="h-16 w-16 text-blue-600 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                Premium Feature
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {subscriptionStatus?.message || `Access to ${featureName} requires a premium subscription.`}
              </p>
              <div className="space-y-4">
                <button
                  onClick={() => router.push(redirectTo)}
                  className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <Crown className="h-5 w-5" />
                  Upgrade to Premium
                  <ArrowRight className="h-4 w-4" />
                </button>
                <button
                  onClick={() => router.back()}
                  className="w-full px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Redirect to upgrade page
    router.push(redirectTo);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Redirecting to upgrade...</p>
        </div>
      </div>
    );
  }

  // User has premium access - render children
  return <>{children}</>;
}

export default PremiumGuard;
