'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Crown, Zap, Star, AlertCircle } from 'lucide-react';
import Link from 'next/link';

interface SubscriptionData {
  hasAccess: boolean;
  subscription?: {
    tier: string;
    status: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
  };
  upgradeUrl?: string;
  message?: string;
}

export function SubscriptionStatus() {
  const { data: session, status } = useSession();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      setLoading(false);
      return;
    }

    fetchSubscriptionStatus();
  }, [session, status]);

  const fetchSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/subscription/status');
      if (response.ok) {
        const data = await response.json();
        setSubscriptionData(data.data);
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  if (!subscriptionData?.hasAccess) {
    return (
      <Link href="/subscription">
        <Badge variant="outline" className="cursor-pointer hover:bg-blue-50 border-blue-200 text-blue-700">
          <Zap className="w-3 h-3 mr-1" />
          Upgrade
        </Badge>
      </Link>
    );
  }

  const subscription = subscriptionData.subscription!;
  const tierName = subscription.tier.replace('_', ' ').toLowerCase()
    .replace(/\b\w/g, l => l.toUpperCase());

  const getTierIcon = () => {
    switch (subscription.tier) {
      case 'QUICK_START':
        return <Zap className="w-3 h-3 mr-1" />;
      case 'CAREER_TRANSITION':
        return <Star className="w-3 h-3 mr-1" />;
      case 'CAREER_PARTNER':
        return <Crown className="w-3 h-3 mr-1" />;
      default:
        return <Star className="w-3 h-3 mr-1" />;
    }
  };

  const getTierColor = () => {
    switch (subscription.tier) {
      case 'QUICK_START':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'CAREER_TRANSITION':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'CAREER_PARTNER':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  // Show warning if subscription expires soon
  const showWarning = subscription.daysRemaining <= 7;

  return (
    <div className="flex items-center space-x-2">
      <Badge className={getTierColor()}>
        {getTierIcon()}
        {tierName}
      </Badge>
      
      {showWarning && (
        <Badge variant="outline" className="border-yellow-200 text-yellow-700 bg-yellow-50">
          <AlertCircle className="w-3 h-3 mr-1" />
          {subscription.daysRemaining}d left
        </Badge>
      )}
    </div>
  );
}

export function SubscriptionStatusMobile() {
  const { data: session, status } = useSession();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      setLoading(false);
      return;
    }

    fetchSubscriptionStatus();
  }, [session, status]);

  const fetchSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/subscription/status');
      if (response.ok) {
        const data = await response.json();
        setSubscriptionData(data.data);
      }
    } catch (error) {
      console.error('Error fetching subscription status:', error);
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="px-4 py-2">
        <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  if (!subscriptionData?.hasAccess) {
    return (
      <div className="px-4 py-2 border-t border-gray-200">
        <Link href="/subscription">
          <Button variant="outline" size="sm" className="w-full">
            <Zap className="w-4 h-4 mr-2" />
            Upgrade to Premium
          </Button>
        </Link>
      </div>
    );
  }

  const subscription = subscriptionData.subscription!;
  const tierName = subscription.tier.replace('_', ' ').toLowerCase()
    .replace(/\b\w/g, l => l.toUpperCase());

  return (
    <div className="px-4 py-2 border-t border-gray-200">
      <div className="text-sm text-gray-600 mb-1">Subscription</div>
      <div className="flex items-center justify-between">
        <span className="font-medium text-sm">{tierName}</span>
        <span className="text-xs text-gray-500">
          {subscription.daysRemaining} days left
        </span>
      </div>
      {subscription.daysRemaining <= 7 && (
        <div className="mt-1 text-xs text-yellow-600">
          <AlertCircle className="w-3 h-3 inline mr-1" />
          Expires soon
        </div>
      )}
    </div>
  );
}
