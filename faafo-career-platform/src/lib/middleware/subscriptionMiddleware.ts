import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SubscriptionService } from '@/lib/services/subscriptionService';

export interface SubscriptionCheckResult {
  hasAccess: boolean;
  user?: {
    id: string;
    email: string;
  };
  subscription?: any;
  error?: string;
}

/**
 * Middleware to check if user has active subscription for premium features
 */
export async function withSubscriptionCheck(
  request: NextRequest,
  handler: (request: NextRequest, checkResult: SubscriptionCheckResult) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Check authentication first
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
          message: 'Please sign in to access this feature',
        },
        { status: 401 }
      );
    }

    // Check subscription status
    const hasActiveSubscription = await SubscriptionService.hasActiveSubscription(session.user.id);
    
    if (!hasActiveSubscription) {
      return NextResponse.json(
        {
          success: false,
          error: 'Premium subscription required',
          code: 'SUBSCRIPTION_REQUIRED',
          message: 'This feature requires an active subscription. Please upgrade to continue.',
          upgradeUrl: '/subscription',
        },
        { status: 403 }
      );
    }

    // Get subscription details
    const subscription = await SubscriptionService.getUserSubscription(session.user.id);

    const checkResult: SubscriptionCheckResult = {
      hasAccess: true,
      user: {
        id: session.user.id,
        email: session.user.email!,
      },
      subscription,
    };

    return await handler(request, checkResult);
  } catch (error) {
    console.error('Subscription check error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Subscription verification failed',
        code: 'SUBSCRIPTION_CHECK_ERROR',
        message: 'Unable to verify subscription status. Please try again.',
      },
      { status: 500 }
    );
  }
}

/**
 * Check subscription status without throwing errors (for conditional features)
 */
export async function checkSubscriptionStatus(userId: string): Promise<SubscriptionCheckResult> {
  try {
    const hasActiveSubscription = await SubscriptionService.hasActiveSubscription(userId);
    
    if (!hasActiveSubscription) {
      return {
        hasAccess: false,
        error: 'No active subscription',
      };
    }

    const subscription = await SubscriptionService.getUserSubscription(userId);
    
    return {
      hasAccess: true,
      subscription,
    };
  } catch (error) {
    console.error('Subscription status check error:', error);
    return {
      hasAccess: false,
      error: 'Failed to check subscription status',
    };
  }
}

/**
 * Premium feature wrapper for API routes
 */
export function requirePremiumSubscription(
  handler: (request: NextRequest, checkResult: SubscriptionCheckResult) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    return withSubscriptionCheck(request, handler);
  };
}

/**
 * Subscription status response for frontend
 */
export interface SubscriptionStatusResponse {
  hasAccess: boolean;
  subscription?: {
    tier: string;
    status: string;
    endDate: string;
    daysRemaining: number;
    isActive: boolean;
  };
  upgradeUrl?: string;
  message?: string;
}

/**
 * Get subscription status for frontend components
 */
export async function getSubscriptionStatusForUser(userId: string): Promise<SubscriptionStatusResponse> {
  try {
    const subscription = await SubscriptionService.getUserSubscription(userId);
    
    if (!subscription || !subscription.isActive) {
      return {
        hasAccess: false,
        upgradeUrl: '/subscription',
        message: 'Upgrade to access premium features',
      };
    }

    return {
      hasAccess: true,
      subscription: {
        tier: subscription.tier,
        status: subscription.status,
        endDate: subscription.endDate.toISOString(),
        daysRemaining: subscription.daysRemaining,
        isActive: subscription.isActive,
      },
    };
  } catch (error) {
    console.error('Error getting subscription status:', error);
    return {
      hasAccess: false,
      message: 'Unable to verify subscription status',
    };
  }
}

/**
 * List of API endpoints that require premium subscription
 */
export const PREMIUM_ENDPOINTS = [
  '/api/ai/skills-analysis',
  '/api/ai/skills-analysis/comprehensive',
  '/api/ai/career-recommendations',
  '/api/ai/resume-analysis',
  '/api/interview-practice/*/questions',
  '/api/assessment/*/enhanced-results',
  '/api/assessment/*/ai-insights',
] as const;

/**
 * Check if an endpoint requires premium subscription
 */
export function isPremiumEndpoint(pathname: string): boolean {
  return PREMIUM_ENDPOINTS.some(endpoint => {
    // Convert endpoint pattern to regex
    const pattern = endpoint.replace(/\*/g, '[^/]+');
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(pathname);
  });
}

export default {
  withSubscriptionCheck,
  checkSubscriptionStatus,
  requirePremiumSubscription,
  getSubscriptionStatusForUser,
  isPremiumEndpoint,
};
