import Stripe from 'stripe';
import { CONFIG } from '@/lib/config';

// Initialize Stripe with your secret key
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
  typescript: true,
});

export interface SubscriptionPackage {
  id: string;
  name: string;
  description: string;
  price: number; // in cents
  duration: number; // in days
  productId: string;
  tier: 'QUICK_START' | 'CAREER_TRANSITION' | 'CAREER_PARTNER';
}

export const SUBSCRIPTION_PACKAGES: SubscriptionPackage[] = [
  {
    id: 'quick-start',
    name: 'Quick Start Package',
    description: '30-day access to FAAFO Career Platform with AI-powered career assessment, resume builder, and interview practice',
    price: CONFIG.STRIPE.QUICK_START_PRICE,
    duration: CONFIG.STRIPE.QUICK_START_DAYS,
    productId: CONFIG.STRIPE.QUICK_START_PRODUCT_ID,
    tier: 'QUICK_START',
  },
  {
    id: 'career-transition',
    name: 'Career Transition Package',
    description: '90-day access to FAAFO Career Platform with complete AI-powered career guidance, skill gap analysis, and personalized learning paths',
    price: CONFIG.STRIPE.CAREER_TRANSITION_PRICE,
    duration: CONFIG.STRIPE.CAREER_TRANSITION_DAYS,
    productId: CONFIG.STRIPE.CAREER_TRANSITION_PRODUCT_ID,
    tier: 'CAREER_TRANSITION',
  },
  {
    id: 'career-partner',
    name: 'Career Partner Package',
    description: '365-day access to FAAFO Career Platform with unlimited AI features, priority support, and comprehensive career development tools',
    price: CONFIG.STRIPE.CAREER_PARTNER_PRICE,
    duration: CONFIG.STRIPE.CAREER_PARTNER_DAYS,
    productId: CONFIG.STRIPE.CAREER_PARTNER_PRODUCT_ID,
    tier: 'CAREER_PARTNER',
  },
];

export class StripeService {
  /**
   * Create a Stripe customer
   */
  static async createCustomer(email: string, name?: string): Promise<Stripe.Customer> {
    return await stripe.customers.create({
      email,
      name: name || undefined,
      metadata: {
        source: 'faafo-career-platform',
      },
    });
  }

  /**
   * Create a payment intent for a subscription package
   */
  static async createPaymentIntent(
    customerId: string,
    packageId: string,
    metadata?: Record<string, string>
  ): Promise<Stripe.PaymentIntent> {
    const package_ = SUBSCRIPTION_PACKAGES.find(p => p.id === packageId);
    if (!package_) {
      throw new Error(`Invalid package ID: ${packageId}`);
    }

    return await stripe.paymentIntents.create({
      amount: package_.price,
      currency: 'usd',
      customer: customerId,
      description: package_.description,
      metadata: {
        packageId,
        tier: package_.tier,
        duration: package_.duration.toString(),
        productId: package_.productId,
        ...metadata,
      },
      automatic_payment_methods: {
        enabled: true,
      },
    });
  }

  /**
   * Retrieve a payment intent
   */
  static async getPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    return await stripe.paymentIntents.retrieve(paymentIntentId);
  }

  /**
   * Get customer by ID
   */
  static async getCustomer(customerId: string): Promise<Stripe.Customer> {
    const customer = await stripe.customers.retrieve(customerId);
    if (customer.deleted) {
      throw new Error('Customer has been deleted');
    }
    return customer as Stripe.Customer;
  }

  /**
   * Update customer
   */
  static async updateCustomer(
    customerId: string,
    updates: Stripe.CustomerUpdateParams
  ): Promise<Stripe.Customer> {
    return await stripe.customers.update(customerId, updates);
  }

  /**
   * Create a checkout session for a package
   */
  static async createCheckoutSession(
    customerId: string,
    packageId: string,
    successUrl: string,
    cancelUrl: string,
    metadata?: Record<string, string>
  ): Promise<Stripe.Checkout.Session> {
    const package_ = SUBSCRIPTION_PACKAGES.find(p => p.id === packageId);
    if (!package_) {
      throw new Error(`Invalid package ID: ${packageId}`);
    }

    return await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product: package_.productId,
            unit_amount: package_.price,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        packageId,
        tier: package_.tier,
        duration: package_.duration.toString(),
        ...metadata,
      },
    });
  }

  /**
   * Retrieve a checkout session
   */
  static async getCheckoutSession(sessionId: string): Promise<Stripe.Checkout.Session> {
    return await stripe.checkout.sessions.retrieve(sessionId);
  }

  /**
   * Get package by ID
   */
  static getPackage(packageId: string): SubscriptionPackage | undefined {
    return SUBSCRIPTION_PACKAGES.find(p => p.id === packageId);
  }

  /**
   * Update a payment intent
   */
  static async updatePaymentIntent(
    paymentIntentId: string,
    updates: Stripe.PaymentIntentUpdateParams
  ): Promise<Stripe.PaymentIntent> {
    return await stripe.paymentIntents.update(paymentIntentId, updates);
  }

  /**
   * Get all packages
   */
  static getAllPackages(): SubscriptionPackage[] {
    return SUBSCRIPTION_PACKAGES;
  }

  /**
   * Calculate subscription end date
   */
  static calculateEndDate(startDate: Date, durationDays: number): Date {
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + durationDays);
    return endDate;
  }

  /**
   * Check if subscription is active
   */
  static isSubscriptionActive(endDate: Date): boolean {
    return new Date() < endDate;
  }
}

export default StripeService;
