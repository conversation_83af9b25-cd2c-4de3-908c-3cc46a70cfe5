import { prisma } from '@/lib/prisma';
import { StripeService, SUBSCRIPTION_PACKAGES } from './stripeService';
import type { SubscriptionStatus, SubscriptionTier } from '@prisma/client';

export interface CreateSubscriptionData {
  userId: string;
  packageId: string;
  stripePaymentIntentId?: string;
  stripeCustomerId?: string;
}

export interface SubscriptionWithDetails {
  id: string;
  userId: string;
  tier: SubscriptionTier;
  status: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  daysRemaining: number;
  package: {
    name: string;
    description: string;
    price: number;
  };
}

export class SubscriptionService {
  /**
   * Create a new subscription after successful payment
   */
  static async createSubscription(data: CreateSubscriptionData): Promise<any> {
    const package_ = StripeService.getPackage(data.packageId);
    if (!package_) {
      throw new Error(`Invalid package ID: ${data.packageId}`);
    }

    const startDate = new Date();
    const endDate = StripeService.calculateEndDate(startDate, package_.duration);

    // Create subscription record
    const subscription = await prisma.subscription.create({
      data: {
        userId: data.userId,
        stripeProductId: package_.productId,
        tier: package_.tier,
        status: 'ACTIVE',
        startDate,
        endDate,
        autoRenew: false,
        metadata: {
          packageId: data.packageId,
          packageName: package_.name,
          duration: package_.duration,
        },
      },
    });

    // Update user subscription status
    await prisma.user.update({
      where: { id: data.userId },
      data: {
        subscriptionStatus: 'ACTIVE',
        subscriptionTier: package_.tier,
        subscriptionExpiresAt: endDate,
        subscriptionStartedAt: startDate,
        stripeCustomerId: data.stripeCustomerId,
      },
    });

    // Create payment record if payment intent provided
    if (data.stripePaymentIntentId) {
      await prisma.payment.create({
        data: {
          userId: data.userId,
          subscriptionId: subscription.id,
          stripePaymentIntentId: data.stripePaymentIntentId,
          amount: package_.price,
          currency: 'usd',
          status: 'COMPLETED',
          description: `Payment for ${package_.name}`,
          paidAt: new Date(),
          metadata: {
            packageId: data.packageId,
            tier: package_.tier,
          },
        },
      });
    }

    return subscription;
  }

  /**
   * Get user's active subscription
   */
  static async getUserSubscription(userId: string): Promise<SubscriptionWithDetails | null> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        subscriptionStatus: true,
        subscriptionTier: true,
        subscriptionExpiresAt: true,
        subscriptionStartedAt: true,
        subscriptions: {
          where: {
            status: 'ACTIVE',
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
        },
      },
    });

    if (!user || !user.subscriptions.length) {
      return null;
    }

    const subscription = user.subscriptions[0];
    const package_ = SUBSCRIPTION_PACKAGES.find(p => p.tier === subscription.tier);
    
    if (!package_) {
      return null;
    }

    const now = new Date();
    const isActive = subscription.endDate > now;
    const daysRemaining = Math.max(0, Math.ceil((subscription.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

    return {
      id: subscription.id,
      userId: subscription.userId,
      tier: subscription.tier,
      status: subscription.status,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      isActive,
      daysRemaining,
      package: {
        name: package_.name,
        description: package_.description,
        price: package_.price,
      },
    };
  }

  /**
   * Check if user has active subscription
   */
  static async hasActiveSubscription(userId: string): Promise<boolean> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        subscriptionStatus: true,
        subscriptionExpiresAt: true,
      },
    });

    if (!user || user.subscriptionStatus !== 'ACTIVE' || !user.subscriptionExpiresAt) {
      return false;
    }

    return user.subscriptionExpiresAt > new Date();
  }

  /**
   * Check if user can access premium features
   */
  static async canAccessPremiumFeatures(userId: string): Promise<boolean> {
    return await this.hasActiveSubscription(userId);
  }

  /**
   * Expire user subscription
   */
  static async expireSubscription(userId: string): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: {
        subscriptionStatus: 'EXPIRED',
        subscriptionTier: null,
      },
    });

    await prisma.subscription.updateMany({
      where: {
        userId,
        status: 'ACTIVE',
      },
      data: {
        status: 'EXPIRED',
      },
    });
  }

  /**
   * Get subscription statistics
   */
  static async getSubscriptionStats() {
    const [totalActive, totalExpired, totalRevenue] = await Promise.all([
      prisma.subscription.count({
        where: { status: 'ACTIVE' },
      }),
      prisma.subscription.count({
        where: { status: 'EXPIRED' },
      }),
      prisma.payment.aggregate({
        where: { status: 'COMPLETED' },
        _sum: { amount: true },
      }),
    ]);

    return {
      totalActive,
      totalExpired,
      totalRevenue: totalRevenue._sum.amount || 0,
    };
  }

  /**
   * Clean up expired subscriptions (run as cron job)
   */
  static async cleanupExpiredSubscriptions(): Promise<number> {
    const expiredUsers = await prisma.user.findMany({
      where: {
        subscriptionStatus: 'ACTIVE',
        subscriptionExpiresAt: {
          lt: new Date(),
        },
      },
      select: { id: true },
    });

    if (expiredUsers.length === 0) {
      return 0;
    }

    const userIds = expiredUsers.map(u => u.id);

    // Update users
    await prisma.user.updateMany({
      where: { id: { in: userIds } },
      data: {
        subscriptionStatus: 'EXPIRED',
        subscriptionTier: null,
      },
    });

    // Update subscriptions
    await prisma.subscription.updateMany({
      where: {
        userId: { in: userIds },
        status: 'ACTIVE',
      },
      data: {
        status: 'EXPIRED',
      },
    });

    return expiredUsers.length;
  }
}

export default SubscriptionService;
